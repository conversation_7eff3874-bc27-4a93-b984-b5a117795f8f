package com.example.chat;

import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import com.vaadin.server.VaadinServlet;

public class VaadinServer {
    public static void main(String[] args) throws Exception {
        Server server = new Server(8080);
        
        ServletContextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
        context.setContextPath("/");
        server.setHandler(context);
        
        ServletHolder servletHolder = new ServletHolder(new VaadinServlet());
        servletHolder.setInitParameter("UI", "com.example.chat.ChatUI");
        context.addServlet(servletHolder, "/*");
        
        System.out.println("Starting Vaadin Chat Client on http://localhost:8080");
        server.start();
        server.join();
    }
}
