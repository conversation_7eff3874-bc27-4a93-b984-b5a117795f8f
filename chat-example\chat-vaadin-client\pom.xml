<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>chat-example</artifactId>
    <groupId>com.example.chat</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <groupId>org.test</groupId>
  <artifactId>chat-vaadin-client</artifactId>
  <packaging>war</packaging>
  <version>1.0-SNAPSHOT</version>

  <prerequisites>
    <maven>3</maven>
  </prerequisites>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>

    <jetty.plugin.version>9.4.15.v20190215</jetty.plugin.version>
    <vaadin.version>8.1.6</vaadin.version>
    <!-- If there are no local customisations, this can also be "fetch" or "cdn" -->
    <vaadin.widgetset.mode>cdn</vaadin.widgetset.mode>
  </properties>

  <repositories>
    <repository>
      <id>vaadin-addons</id>
      <url>http://maven.vaadin.com/vaadin-addons</url>
    </repository>
  </repositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.vaadin</groupId>
        <artifactId>vaadin-bom</artifactId>
        <version>${vaadin.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>3.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.vaadin</groupId>
      <artifactId>vaadin-server</artifactId>
    </dependency>
    <dependency>
      <groupId>com.vaadin</groupId>
      <artifactId>vaadin-push</artifactId>
    </dependency>
    <dependency>
      <groupId>com.vaadin</groupId>
      <artifactId>vaadin-client-compiled</artifactId>
    </dependency>
    <dependency>
      <groupId>com.vaadin</groupId>
      <artifactId>vaadin-themes</artifactId>
    </dependency>
    <dependency>
      <groupId>com.example.chat</groupId>
      <artifactId>chat-server</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!--Development-->

    <dependency>
      <groupId>org.rapidpm.microservice</groupId>
      <artifactId>rapidpm-microservice-modules-core</artifactId>
      <version>0.9.1</version>
      <scope>test</scope>
    </dependency>
    <!--TDD stuff-->
    <dependency>
      <groupId>org.rapidpm</groupId>
      <artifactId>rapidpm-functional-reactive</artifactId>
      <version>0.0.6</version>
      <scope>test</scope>
    </dependency>
      <dependency>
          <groupId>org.eclipse.jetty</groupId>
          <artifactId>jetty-servlet</artifactId>
          <version>9.4.46.v20220331</version>
          <scope>compile</scope>
      </dependency>

  </dependencies>

  <build>
    <plugins>
      <!--Vaadin -->

      <plugin>
        <groupId>com.vaadin</groupId>
        <artifactId>vaadin-maven-plugin</artifactId>
        <version>${vaadin.version}</version>
        <configuration>
          <extraJvmArgs>-Xmx512M -Xss1024k</extraJvmArgs>
          <webappDirectory>
            ${basedir}/target/classes/VAADIN/widgetsets
          </webappDirectory>
          <draftCompile>true</draftCompile>
          <compileReport>false</compileReport>
          <!-- Change to PRETTY (or possibly DETAILED) to get
          unobfuscated client side stack traces. A better approach
          for debugging is to use Super Dev Mode -->
          <style>OBF</style>
          <strict>true</strict>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>update-widgetset</goal>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.2.2</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <!-- Exclude an unnecessary file generated by the GWT compiler. -->
          <packagingExcludes>WEB-INF/classes/VAADIN/widgetsets/WEB-INF/**</packagingExcludes>
        </configuration>
      </plugin>
      <!--<plugin>-->
        <!--<groupId>com.vaadin</groupId>-->
        <!--<artifactId>vaadin-maven-plugin</artifactId>-->
        <!--<version>${vaadin.plugin.version}</version>-->
        <!--<executions>-->
          <!--<execution>-->
            <!--<goals>-->
              <!--<goal>update-theme</goal>-->
              <!--<goal>update-widgetset</goal>-->
              <!--<goal>compile</goal>-->
              <!--&lt;!&ndash; Comment out compile-theme goal to use on-the-fly theme compilation &ndash;&gt;-->
              <!--<goal>compile-theme</goal>-->
            <!--</goals>-->
          <!--</execution>-->
        <!--</executions>-->
      <!--</plugin>-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.1.0</version>
        <!-- Clean up also any pre-compiled themes -->
        <configuration>
          <filesets>
            <fileset>
              <directory>src/main/webapp/VAADIN/themes</directory>
              <includes>
                <include>**/styles.css</include>
                <include>**/styles.scss.cache</include>
              </includes>
            </fileset>
          </filesets>
        </configuration>
      </plugin>

      <!-- The Jetty plugin allows us to easily test the development build by
        running jetty:run on the command line. -->
      <plugin>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-maven-plugin</artifactId>
        <version>${jetty.plugin.version}</version>
        <configuration>
          <scanIntervalSeconds>2</scanIntervalSeconds>
          <httpConnector>
            <port>8080</port>
          </httpConnector>
          <stopKey>stop</stopKey>
          <stopPort>9999</stopPort>
        </configuration>
      </plugin>
    </plugins>
  </build>



</project>
